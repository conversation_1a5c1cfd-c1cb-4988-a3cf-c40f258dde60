import { useEffect, useMemo, useState, useCallback } from "react";
import { store } from "../store/tinybase";
import { loadFood } from "../store/loader";

export interface Food {
  id: string;
  secure_id: string;
  name: string;
  percent_ms: number;
  price_ton: number;
  type: "feed" | "food" | "mix";
  created_at: string;
  updated_at: string;
}

export function useFoods() {
  const [foods, setFoods] = useState<Food[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateFoodsFromStore = useCallback(() => {
    const foodTable = store.getTable("food");
    const foodList: Food[] = Object.entries(foodTable).map(([id, data]) => ({
      id,
      ...(data as Omit<Food, "id">),
    }));
    setFoods(foodList);
    return foodList;
  }, []);

  const loadFoodsIfNeeded = useCallback(async () => {
    // Verificar se já temos dados no store
    const currentFoods = updateFoodsFromStore();
    if (currentFoods.length > 0) {
      console.log(
        "Alimentos já carregados no TinyBase, usando dados existentes"
      );
      return;
    }

    // Se não temos dados, carregar do banco
    setLoading(true);
    setError(null);

    try {
      console.log("Carregando alimentos do banco de dados...");
      await loadFood();
      updateFoodsFromStore();
      console.log("Alimentos carregados com sucesso");
    } catch (error) {
      console.error("Erro ao carregar alimentos:", error);
      setError(error instanceof Error ? error.message : "Erro desconhecido");
    } finally {
      setLoading(false);
    }
  }, [updateFoodsFromStore]);

  useEffect(() => {
    // Carregar dados na primeira vez que o hook é usado
    loadFoodsIfNeeded();

    // Escutar mudanças na tabela de alimentos
    const listenerId = store.addTableListener("food", () => {
      updateFoodsFromStore();
    });

    return () => {
      store.delListener(listenerId);
    };
  }, [loadFoodsIfNeeded, updateFoodsFromStore]);

  const foodItems = useMemo(() => {
    return foods.map((food) => ({
      label: food.name,
      value: food.id,
      type: food.type,
      price_ton: food.price_ton,
      secure_id: food.secure_id,
    }));
  }, [foods]);

  return {
    foods,
    foodItems,
    loading,
    error,
    refresh: loadFoodsIfNeeded,
  };
}
