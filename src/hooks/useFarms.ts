import { useEffect, useMemo, useState, useCallback } from "react";
import { store } from "../store/tinybase";
import { loadFarms } from "../store/loader";

export interface Farm {
  id: string;
  secure_id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export function useFarms() {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateFarmsFromStore = useCallback(() => {
    const farmTable = store.getTable("farm");
    const farmList: Farm[] = Object.entries(farmTable).map(([id, data]) => ({
      id,
      ...(data as Omit<Farm, "id">),
    }));
    setFarms(farmList);
    return farmList;
  }, []);

  const loadFarmsIfNeeded = useCallback(async () => {
    // Verificar se já temos dados no store
    const currentFarms = updateFarmsFromStore();
    if (currentFarms.length > 0) {
      console.log(
        "Fazendas já carregadas no TinyBase, usando dados existentes"
      );
      return;
    }

    // Se não temos dados, carregar do banco
    setLoading(true);
    setError(null);

    try {
      console.log("Carregando fazendas do banco de dados...");
      await loadFarms();
      updateFarmsFromStore();
      console.log("Fazendas carregadas com sucesso");
    } catch (error) {
      console.error("Erro ao carregar fazendas:", error);
      setError(error instanceof Error ? error.message : "Erro desconhecido");
    } finally {
      setLoading(false);
    }
  }, [updateFarmsFromStore]);

  useEffect(() => {
    // Carregar dados na primeira vez que o hook é usado
    loadFarmsIfNeeded();

    // Escutar mudanças na tabela de fazendas
    const listenerId = store.addTableListener("farm", () => {
      updateFarmsFromStore();
    });

    return () => {
      store.delListener(listenerId);
    };
  }, [loadFarmsIfNeeded, updateFarmsFromStore]);

  const farmItems = useMemo(() => {
    return farms.map((farm) => ({
      label: farm.name,
      value: farm.id,
      secure_id: farm.secure_id,
    }));
  }, [farms]);

  return {
    farms,
    farmItems,
    loading,
    error,
    refresh: loadFarmsIfNeeded,
  };
}
