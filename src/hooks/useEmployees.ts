import { useEffect, useMemo, useState, useCallback } from "react";
import { store } from "../store/tinybase";
import { loadEmployee } from "../store/loader";

export interface Employee {
  id: string;
  secure_id: string;
  function: string;
  name: string;
  farm_id: number;
  created_at: string;
  updated_at: string;
}

export function useEmployees() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateEmployeesFromStore = useCallback(() => {
    const employeeTable = store.getTable("employee");
    const employeeList: Employee[] = Object.entries(employeeTable).map(
      ([id, data]) => ({
        id,
        ...(data as Omit<Employee, "id">),
      })
    );
    setEmployees(employeeList);
    return employeeList;
  }, []);

  const loadEmployeesIfNeeded = useCallback(async () => {
    // Verificar se já temos dados no store
    const currentEmployees = updateEmployeesFromStore();
    if (currentEmployees.length > 0) {
      console.log(
        "Funcionários já carregados no TinyBase, usando dados existentes"
      );
      return;
    }

    // Se não temos dados, carregar do banco
    setLoading(true);
    setError(null);

    try {
      console.log("Carregando funcionários do banco de dados...");
      await loadEmployee();
      updateEmployeesFromStore();
      console.log("Funcionários carregados com sucesso");
    } catch (error) {
      console.error("Erro ao carregar funcionários:", error);
      setError(error instanceof Error ? error.message : "Erro desconhecido");
    } finally {
      setLoading(false);
    }
  }, [updateEmployeesFromStore]);

  useEffect(() => {
    // Carregar dados na primeira vez que o hook é usado
    loadEmployeesIfNeeded();

    // Escutar mudanças na tabela de funcionários
    const listenerId = store.addTableListener("employee", () => {
      updateEmployeesFromStore();
    });

    return () => {
      store.delListener(listenerId);
    };
  }, [loadEmployeesIfNeeded, updateEmployeesFromStore]);

  const employeeItems = useMemo(() => {
    return employees.map((employee) => ({
      label: employee.name,
      value: employee.id,
      function: employee.function,
      secure_id: employee.secure_id,
    }));
  }, [employees]);

  return {
    employees,
    employeeItems,
    loading,
    error,
    refresh: loadEmployeesIfNeeded,
  };
}
