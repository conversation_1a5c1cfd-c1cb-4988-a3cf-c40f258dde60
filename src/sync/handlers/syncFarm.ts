import { AuthApiResponse } from "@/src/contexts/AuthContext";
import { db } from "@/src/db";
import AsyncStorage from "@react-native-async-storage/async-storage";

export async function syncFarm() {
  try {
    const data = await AsyncStorage.getItem("@beef_auth:user");
    const user: AuthApiResponse | null = data ? JSON.parse(data) : null;

    if (!user?.farm) {
      console.log({ user });
      console.log("Não foi possivel obter a fazenda do usuário");
      return null;
    }

    console.log("Verificando se fazenda existe na base local...");

    //save in db
    const farmExists = await db.getFirstAsync(
      "SELECT id FROM farm WHERE id = ?",
      [user.farm.id]
    );

    if (!farmExists) {
      console.log("Inserindo fazenda na base local...");
      await db.runAsync(
        "INSERT INTO farm (id, secure_id, name, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
        [
          user.farm.id,
          user.farm.secure_id,
          user.farm.name,
          new Date().toISOString(),
          new Date().toISOString(),
        ]
      );
      console.log("Fazenda inserida com sucesso");
    } else {
      console.log("Fazenda já existe na base local");
    }
    return;
  } catch (error) {
    console.log("Erro ao sincronizar fazenda:", error);
    throw error;
  }
}
