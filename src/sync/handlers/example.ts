import { db } from "../../db";

export async function syncBreeds() {
  // const lastSyncRow = await db.getFirstAsync(
  //   "SELECT last_sync_at FROM sync_metadata WHERE entity_type = ?",
  //   ["breeds"]
  // );
  // const lastSyncAt = lastSyncRow?.last_sync_at || '1970-01-01T00:00:00Z';
  // const response = await api.get('/breeds', {
  //   params: { updatedAfter: lastSyncAt }
  // });
  // for (const breed of response.data) {
  //   await db.runAsync(
  //     "INSERT OR REPLACE INTO breeds (id, name, updated_at) VALUES (?, ?, ?)",
  //     [breed.id, breed.name, breed.updatedAt]
  //   );
  // }
  // await db.runAsync(
  //   "INSERT OR REPLACE INTO sync_metadata (entity_type, last_sync_at) VALUES (?, ?)",
  //   ["breeds", new Date().toISOString()]
  // );
}
