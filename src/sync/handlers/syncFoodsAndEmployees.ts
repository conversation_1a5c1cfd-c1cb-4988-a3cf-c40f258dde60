import { db } from "@/src/db";
import { FoodSchemaDB } from "@/src/db/types";
import { api } from "@/src/services/api";

type ApiResponse = {
  feeds: Array<{
    secure_id: string;
    name: string;
    type: "feed" | "food" | "mix";
    percent_ms: number;
    preMixes: Array<{
      percent_ms: number;
      percent_incl_mo_food: number;
      percent_incl_ms_food: number;
      food: {
        secure_id: string;
        name: string;
        type: "feed" | "food" | "mix";
        percent_ms: number;
        price_ton: number;
      };
    }> | null;
    feeds: Array<{
      percent_incl_mo_food: number;
      order: number;
      food: {
        secure_id: string;
        name: string;
        type: "feed" | "food" | "mix";
        percent_ms: number;
        price_ton: number;
      };
    }>;
  }>;
  employees: Array<{
    secure_id: string;
    name: string;
    function: string;
  }>;
};

export async function syncFoodsAndEmployees() {
  const { data } = await api.get<ApiResponse>(
    "/v1/feedlot/food-management/manufacturingFeed/showData"
  );

  const { feeds, employees } = data;

  try {
    // Start transaction
    await db.execAsync("BEGIN TRANSACTION");

    // Sync foods
    const apiSecureIds = new Set<string>();

    for (const feed of feeds) {
      apiSecureIds.add(feed.secure_id);

      // Check if food exists
      const existingFood = await db.getFirstAsync(
        "SELECT id FROM food WHERE secure_id = ?",
        [feed.secure_id]
      );

      if (existingFood) {
        // Update existing food
        await db.runAsync(
          `UPDATE food SET 
           name = ?, 
           percent_ms = ?, 
           type = ?, 
           updated_at = CURRENT_TIMESTAMP 
           WHERE secure_id = ?`,
          [feed.name, feed.percent_ms, feed.type, feed.secure_id]
        );
      } else {
        // Insert new food
        await db.runAsync(
          `INSERT INTO food (secure_id, name, percent_ms, price_ton, type) 
           VALUES (?, ?, ?, ?, ?)`,
          [feed.secure_id, feed.name, feed.percent_ms, 0, feed.type]
        );
      }

      // Get the food ID for handling preMixes and feeds
      const foodRecord: FoodSchemaDB = await db.getFirstAsync(
        "SELECT id FROM food WHERE secure_id = ?",
        [feed.secure_id]
      );

      if (foodRecord) {
        // Handle preMixes
        if (feed.preMixes && feed.preMixes.length > 0) {
          // Clear existing pre_mix_foods for this feed
          await db.runAsync(
            "DELETE FROM pre_mix_foods WHERE pre_mix_food_id = ?",
            [foodRecord.id]
          );

          // Insert preMixes data
          for (let i = 0; i < feed.preMixes.length; i++) {
            const preMix = feed.preMixes[i];

            // First ensure the preMix food exists
            const preMixFoodExists: FoodSchemaDB = await db.getFirstAsync(
              "SELECT id FROM food WHERE secure_id = ?",
              [preMix.food.secure_id]
            );

            let preMixFoodId;
            if (preMixFoodExists) {
              preMixFoodId = preMixFoodExists.id;
              // Update preMix food data
              await db.runAsync(
                `UPDATE food SET 
                 name = ?, 
                 percent_ms = ?, 
                 price_ton = ?, 
                 type = ?, 
                 updated_at = CURRENT_TIMESTAMP 
                 WHERE secure_id = ?`,
                [
                  preMix.food.name,
                  preMix.food.percent_ms,
                  preMix.food.price_ton,
                  preMix.food.type,
                  preMix.food.secure_id,
                ]
              );
            } else {
              // Insert new preMix food
              const result = await db.runAsync(
                `INSERT INTO food (secure_id, name, percent_ms, price_ton, type) 
                 VALUES (?, ?, ?, ?, ?)`,
                [
                  preMix.food.secure_id,
                  preMix.food.name,
                  preMix.food.percent_ms,
                  preMix.food.price_ton,
                  preMix.food.type,
                ]
              );
              preMixFoodId = result.lastInsertRowId;
            }

            // Insert into pre_mix_foods
            await db.runAsync(
              `INSERT INTO pre_mix_foods 
               (pre_mix_food_id, food_id, percent_incl_mo_food, percent_incl_ms_food, percent_ms, "order") 
               VALUES (?, ?, ?, ?, ?, ?)`,
              [
                foodRecord.id,
                preMixFoodId,
                preMix.percent_incl_mo_food,
                preMix.percent_incl_ms_food,
                preMix.percent_ms,
                i + 1,
              ]
            );

            // Add preMix food secure_id to apiSecureIds
            apiSecureIds.add(preMix.food.secure_id);
          }
        }

        // Handle feeds
        if (feed.feeds && feed.feeds.length > 0) {
          // Clear existing feed_foods for this feed
          await db.runAsync("DELETE FROM feed_foods WHERE feed_food_id = ?", [
            foodRecord.id,
          ]);

          // Insert feeds data
          for (const feedFood of feed.feeds) {
            // First ensure the feed food exists
            const feedFoodExists: FoodSchemaDB = await db.getFirstAsync(
              "SELECT id FROM food WHERE secure_id = ?",
              [feedFood.food.secure_id]
            );

            let feedFoodId;
            if (feedFoodExists) {
              feedFoodId = feedFoodExists.id;
              // Update feed food data
              await db.runAsync(
                `UPDATE food SET 
                 name = ?, 
                 percent_ms = ?, 
                 price_ton = ?, 
                 type = ?, 
                 updated_at = CURRENT_TIMESTAMP 
                 WHERE secure_id = ?`,
                [
                  feedFood.food.name,
                  feedFood.food.percent_ms,
                  feedFood.food.price_ton,
                  feedFood.food.type,
                  feedFood.food.secure_id,
                ]
              );
            } else {
              // Insert new feed food
              const result = await db.runAsync(
                `INSERT INTO food (secure_id, name, percent_ms, price_ton, type) 
                 VALUES (?, ?, ?, ?, ?)`,
                [
                  feedFood.food.secure_id,
                  feedFood.food.name,
                  feedFood.food.percent_ms,
                  feedFood.food.price_ton,
                  feedFood.food.type,
                ]
              );
              feedFoodId = result.lastInsertRowId;
            }

            // Insert into feed_foods
            await db.runAsync(
              `INSERT INTO feed_foods 
               (feed_food_id, food_id, percent_incl_mo_food, "order") 
               VALUES (?, ?, ?, ?)`,
              [
                foodRecord.id,
                feedFoodId,
                feedFood.percent_incl_mo_food,
                feedFood.order,
              ]
            );

            // Add feed food secure_id to apiSecureIds
            apiSecureIds.add(feedFood.food.secure_id);
          }
        }
      }
    }

    // Remove foods that are not in the API response
    const existingFoods = (await db.getAllAsync(
      "SELECT secure_id FROM food"
    )) as Array<{ secure_id: string }>;

    for (const existingFood of existingFoods) {
      if (!apiSecureIds.has(existingFood.secure_id)) {
        await db.runAsync("DELETE FROM food WHERE secure_id = ?", [
          existingFood.secure_id,
        ]);
      }
    }

    // Sync employees
    const apiEmployeeSecureIds = new Set<string>();

    for (const employee of employees) {
      apiEmployeeSecureIds.add(employee.secure_id);

      // Check if employee exists
      const existingEmployee = await db.getFirstAsync(
        "SELECT id FROM employee WHERE secure_id = ?",
        [employee.secure_id]
      );

      if (existingEmployee) {
        // Update existing employee
        await db.runAsync(
          `UPDATE employee SET 
           name = ?, 
           function = ?, 
           updated_at = CURRENT_TIMESTAMP 
           WHERE secure_id = ?`,
          [employee.name, employee.function, employee.secure_id]
        );
      } else {
        // Insert new employee (assuming farm_id = 1 for now, you may need to adjust this)
        await db.runAsync(
          `INSERT INTO employee (secure_id, name, function, farm_id) 
           VALUES (?, ?, ?, ?)`,
          [employee.secure_id, employee.name, employee.function, 1]
        );
      }
    }

    // Remove employees that are not in the API response
    const existingEmployees = (await db.getAllAsync(
      "SELECT secure_id FROM employee"
    )) as Array<{ secure_id: string }>;

    for (const existingEmployee of existingEmployees) {
      if (!apiEmployeeSecureIds.has(existingEmployee.secure_id)) {
        await db.runAsync("DELETE FROM employee WHERE secure_id = ?", [
          existingEmployee.secure_id,
        ]);
      }
    }

    // Commit transaction
    await db.execAsync("COMMIT");

    console.log("Sync FoodsAndEmployees completed successfully");
  } catch (error) {
    // Rollback transaction on error
    await db.execAsync("ROLLBACK");
    console.error("Error during sync:", error);
    throw error;
  }
}
