import { db } from "../db";
import { SyncMetadataSchemaDB } from "../db/types";
import { syncFarm } from "./handlers/syncFarm";
import { syncFoodsAndEmployees } from "./handlers/syncFoodsAndEmployees";
import { processSyncQueue } from "./upload";

export async function maybeSyncDownloads() {
  const now = new Date();
  const hour = now.getHours();
  const today = now.toISOString().split("T")[0];

  const morningRow: SyncMetadataSchemaDB = await db.getFirstAsync(
    "SELECT last_sync_at FROM sync_metadata WHERE entity_type = ?",
    ["download_morning"]
  );
  const afternoonRow: SyncMetadataSchemaDB = await db.getFirstAsync(
    "SELECT last_sync_at FROM sync_metadata WHERE entity_type = ?",
    ["download_afternoon"]
  );

  const lastMorningDate = morningRow?.last_sync_at?.split("T")[0];
  const lastAfternoonDate = afternoonRow?.last_sync_at?.split("T")[0];

  // Janela 1: depois das 5:00
  if (hour >= 5 && lastMorningDate !== today) {
    await syncAllDownloads(); // baixa dados
    await db.runAsync(
      "INSERT OR REPLACE INTO sync_metadata (entity_type, last_sync_at) VALUES (?, ?)",
      ["download_morning", now.toISOString()]
    );
    return; // já fez o sync da manhã
  }

  // Janela 2: depois das 15:00
  if (hour >= 15 && lastAfternoonDate !== today) {
    await syncAllDownloads();
    await db.runAsync(
      "INSERT OR REPLACE INTO sync_metadata (entity_type, last_sync_at) VALUES (?, ?)",
      ["download_afternoon", now.toISOString()]
    );
    return; // já fez o sync da tarde
  }
}

export async function syncAllDownloads() {
  console.log("Iniciando sync de downloads...");
  try {
    await syncFarm();
    await syncFoodsAndEmployees();
    // console.log("Sync de downloads concluído com sucesso");
  } catch (error) {
    // console.error("Erro durante o sync de downloads:", error);
    throw error;
  }
}
