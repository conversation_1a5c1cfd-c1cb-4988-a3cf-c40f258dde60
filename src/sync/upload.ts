import { db } from "../db";
import { api } from "../services/api";
// seu axios/fetch

export async function processSyncQueue() {
  // const pending = await db.getAllAsync(
  //   "SELECT * FROM sync_queue WHERE status = 'pending' ORDER BY created_at ASC"
  // );
  // for (const item of pending) {
  //   try {
  //     const payload = JSON.parse(item.payload);
  //     if (item.entity_type === 'animal_entry' && item.operation === 'CREATE') {
  //       await api.post('/animal-entries', payload);
  //     }
  //     await db.runAsync("UPDATE sync_queue SET status = 'done' WHERE id = ?", [item.id]);
  //   } catch (err) {
  //     // opcional: marcar erro
  //     await db.runAsync("UPDATE sync_queue SET status = 'error' WHERE id = ?", [item.id]);
  //   }
  // }
}
