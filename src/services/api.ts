import axios from 'axios';
import { API_CONFIG } from '@/src/constants/app-config';

export const api = axios.create({
  baseURL: API_CONFIG.apiUrl,
  headers: { 'Content-Type': 'application/json', Accept: 'application/json' }
});

// Helper to set/remove auth header centrally
export const setAuthToken = (token?: string | null) => {
  if (token) {
    api.defaults.headers.common.Authorization = `Bearer ${token}`;
  } else {
    delete api.defaults.headers.common.Authorization;
  }
};
