import { Buffer } from "buffer";
import {
  ScaleProtocolDecoder,
  ScaleReading,
  ScaleFrameExtractionResult,
  ScaleProtocolConfig,
} from "../../types/scaleTypes";
/**
 * Decodificador simplificado para balança KM3-N-V4
 * Protocolo: frames de 26 bytes com STX (0x02) e ETX (0x03)
 * Formato: STX + dados + sign(byte 12) + stable(byte 13) + ... + weight(bytes 16-22) + ... + ETX
 */
export class KM3NV4Decoder implements ScaleProtocolDecoder {
  // Tamanho fixo do frame para o protocolo KM3-N-V4
  private static readonly FRAME_SIZE = 26;
  private static readonly STX = 0x02;
  private static readonly ETX = 0x03;

  constructor() {
    console.log("[KM3NV4] 🔧 Decoder KM3-N-V4 inicializado");
    console.log("[KM3NV4] Protocolo: Frames de 26 bytes com STX/ETX");
  }

  // UUIDs específicos do KM3-N-V4
  private static readonly FFE0_SERVICE = "0000FFE0-0000-1000-8000-00805F9B34FB";
  private static readonly FFE1_CHARACTERISTIC =
    "0000FFE1-0000-1000-8000-00805F9B34FB";

  /**
   * Extrai frames válidos do buffer de dados
   */
  extractFrames(buffer: Buffer): ScaleFrameExtractionResult {
    const frames: Buffer[] = [];
    let cursor = 0;

    // console.log("[KM3NV4] === EXTRACT FRAMES DEBUG ===");
    // console.log("[KM3NV4] Buffer length:", buffer.length);
    // console.log("[KM3NV4] Buffer hex:", buffer.toString("hex"));

    while (cursor < buffer.length) {
      // Procurar pelo STX (0x02)
      let stxIndex = -1;
      for (let i = cursor; i < buffer.length; i++) {
        if (buffer[i] === KM3NV4Decoder.STX) {
          stxIndex = i;
          break;
        }
      }

      // Se não encontrou STX, não há mais frames
      if (stxIndex === -1) {
        break;
      }

      // Verificar se há bytes suficientes para um frame completo
      if (stxIndex + KM3NV4Decoder.FRAME_SIZE > buffer.length) {
        // Frame incompleto, manter no buffer
        break;
      }

      // Extrair o frame potencial
      const frameEnd = stxIndex + KM3NV4Decoder.FRAME_SIZE;
      const potentialFrame = buffer.slice(stxIndex, frameEnd);

      // Verificar se termina com ETX (0x03)
      if (potentialFrame[potentialFrame.length - 1] === KM3NV4Decoder.ETX) {
        frames.push(potentialFrame);
        // console.log(
        //   `[KM3NV4] ✅ Frame encontrado em posição ${stxIndex}:`,
        //   potentialFrame.toString("hex")
        // );
        cursor = frameEnd;
      } else {
        // STX encontrado mas frame inválido (sem ETX correto)
        // console.log(
        //   `[KM3NV4] ❌ Frame inválido em posição ${stxIndex} (sem ETX correto)`
        // );
        cursor = stxIndex + 1;
      }
    }

    // O resto é tudo que não foi processado
    const rest =
      cursor < buffer.length ? buffer.slice(cursor) : Buffer.alloc(0);

    // console.log("[KM3NV4] Frames extraídos:", frames.length);
    // console.log("[KM3NV4] Resto do buffer (length):", rest.length);
    // if (rest.length > 0) {
    //   console.log("[KM3NV4] Resto hex:", rest.toString("hex"));
    // }
    // console.log("[KM3NV4] === FIM EXTRACT FRAMES DEBUG ===\n");

    return {
      frames,
      rest,
    };
  }

  /**
   * Decodifica um frame em dados de leitura da balança
   */
  parseFrame(frame: Buffer): ScaleReading | null {
    // console.log("[KM3NV4] === PARSE FRAME DEBUG ===");
    // console.log("[KM3NV4] Frame para parsing (length):", frame.length);
    // console.log("[KM3NV4] Frame hex:", frame.toString("hex"));

    try {
      const hexData = frame.toString("hex");
      const weightData = this.parseKM3N(hexData);

      const result: ScaleReading = {
        weight: weightData.weight,
        stable: weightData.stable,
        unit: "kg",
        sign: weightData.sign as 1 | -1,
      };

      // console.log("[KM3NV4] Resultado final do parsing:", result);
      // console.log("[KM3NV4] === FIM PARSE FRAME DEBUG ===\n");

      return result;
    } catch (error) {
      // console.log("[KM3NV4] ❌ Erro durante parsing:", error);
      // console.log("[KM3NV4] === FIM PARSE FRAME DEBUG ===\n");
      return null;
    }
  }

  /**
   * Retorna a configuração do protocolo
   */
  getProtocolConfig(): ScaleProtocolConfig {
    return {
      name: "KM3-N-V4",
      serviceUUIDs: [KM3NV4Decoder.FFE0_SERVICE],
      characteristicUUIDs: [KM3NV4Decoder.FFE1_CHARACTERISTIC],
      startCommand: "P", // Comando padrão - ajustar conforme necessário
      mtuSize: 185, // MTU padrão - ajustar conforme necessário
    };
  }

  private parseKM3N(hex: string) {
    // Converte hex em array de bytes
    const bytes = hex.match(/.{1,2}/g)?.map((b) => parseInt(b, 16)) || [];

    if (bytes[0] !== 0x02 || bytes[bytes.length - 1] !== 0x03) {
      throw new Error("Pacote inválido (faltando STX/ETX).");
    }

    // Campos segundo o protocolo
    const sign = String.fromCharCode(bytes[12]); // '+' ou '-'
    const signalWeight = bytes[12]; // 0=positivo, 1=negativo
    const stable = bytes[13] === 0x31; // 0=não estável, 1=estável
    const weightStr = bytes
      .slice(16, 23)
      .map((b) => String.fromCharCode(b))
      .join("")
      .replace(/^0+/, ""); // remove zeros à esquerda

    const weight = parseInt(weightStr || "0", 10);

    const isNegative = signalWeight === 0x31;
    return {
      weight,
      stable,
      sign: isNegative ? -1 : 1,
    };
  }
}
