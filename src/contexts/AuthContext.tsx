import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { api, setAuthToken } from "@/src/services/api";
import AsyncStorage from "@react-native-async-storage/async-storage";

// ---- Types based on provided login response example ----
export interface Avatar {
  secure_id: string;
  file: string;
  name: string;
  type: string;
  subtype: string;
  created_at: string;
  updated_at: string;
  url: string;
}

export interface PowerUp {
  name: string;
  status: number;
}

export interface Farm {
  id?: number; // present only in current farm object
  secure_id: string;
  name: string;
  active?: number; // present in farms array entries
  powerUps: PowerUp[];
}

export interface TokenInfo {
  type: string;
  token: string;
  expires_at: string;
}

export interface AuthApiResponse {
  name: string;
  email: string;
  avatar?: Avatar | null;
  farm: Farm;
  farms: Farm[];
  token: TokenInfo;
  permissions: string[];
  roles: string[];
}

interface AuthState {
  user: AuthApiResponse | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<boolean>; // returns success
  signOut: () => void;
  isRestoring: boolean; // true enquanto restaura token do storage
}

const AuthContext = createContext<AuthState | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthApiResponse | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRestoring, setIsRestoring] = useState(true);

  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const { data } = await api.post<AuthApiResponse>("/v1/feedlot/auth", {
        email,
        password,
      });
      const authToken = data?.token?.token || null;
      setUser(data);
      setToken(authToken);
      setAuthToken(authToken || undefined);

      if (authToken) {
        await AsyncStorage.setItem("@beef_auth:token", authToken);
        await AsyncStorage.setItem("@beef_auth:user", JSON.stringify(data));
      }
      return true;
    } catch (e: any) {
      const message =
        e?.response?.data?.message || e?.message || "Erro inesperado";
      setError(message);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const signOut = useCallback(() => {
    setUser(null);
    setToken(null);
    setError(null);
    setAuthToken(undefined);
    AsyncStorage.multiRemove(["@beef_auth:token", "@beef_auth:user"]).catch(
      () => {}
    );
  }, []);

  // Carrega token salvo ao iniciar
  useEffect(() => {
    (async () => {
      try {
        const [savedToken, savedUser] = await AsyncStorage.multiGet([
          "@beef_auth:token",
          "@beef_auth:user",
        ]);
        const tokenValue = savedToken?.[1];
        const userJson = savedUser?.[1];
        if (tokenValue) {
          setToken(tokenValue);
          setAuthToken(tokenValue);
        }
        if (userJson) {
          try {
            setUser(JSON.parse(userJson));
          } catch (_) {}
        }
      } catch (_) {
        // ignore
      } finally {
        setIsRestoring(false);
      }
    })();
  }, []);

  const value: AuthState = {
    user,
    token,
    loading,
    error,
    signIn,
    signOut,
    isRestoring,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthState => {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error("useAuth deve ser usado dentro de um AuthProvider");
  return ctx;
};
