// initializeBackgroundTask.js

import * as BackgroundTask from "expo-background-task";
import * as TaskManager from "expo-task-manager";
import { closeDB, initDB } from "../db";
import { maybeSyncDownloads } from "../sync/syncManager";
import NetInfo from "@react-native-community/netinfo";
import AsyncStorage from "@react-native-async-storage/async-storage";

const BACKGROUND_TASK_IDENTIFIER = "com.beefsystem.backgroundsync";
const MINIMUM_INTERVAL = 120;

TaskManager.defineTask(BACKGROUND_TASK_IDENTIFIER, async () => {
  console.log("--- [BEEFSYNC] Tarefa de fundo iniciada pelo sistema! ---");

  // 1. Verifique as condições de execução primeiro.
  const networkState = await NetInfo.fetch();
  if (!networkState.isConnected) {
    console.log("[BEEFSYNC]: Sem conexão com a internet. Finalizando.");
    // É importante retornar um resultado para o sistema operacional.
    return BackgroundTask.BackgroundTaskResult.Failed;
  }

  const userToken = await AsyncStorage.getItem("@beef_auth:token");
  if (!userToken) {
    console.log("[BEEFSYNC]: Nenhum usuário logado. Finalizando.");
    return BackgroundTask.BackgroundTaskResult.Failed;
  }

  // 2. Execute a lógica principal.
  try {
    await initDB();
    console.log("[BEEFSYNC] BD inicializado.");

    await maybeSyncDownloads();

    console.log("[BEEFSYNC] Tarefa executada com sucesso.");
    return BackgroundTask.BackgroundTaskResult.Success; // Sucesso com novos dados
  } catch (error) {
    console.error("[BEEFSYNC] Erro no background task", error);
    return BackgroundTask.BackgroundTaskResult.Failed; // Falha
  } finally {
    await closeDB();
    console.log("[BEEFSYNC] BD fechado.");
  }
});

// Esta função agora APENAS registra a tarefa.
export const initializeBackgroundTask = async () => {
  try {
    const isRegistered = await TaskManager.isTaskRegisteredAsync(
      BACKGROUND_TASK_IDENTIFIER
    );
    if (!isRegistered) {
      await BackgroundTask.registerTaskAsync(BACKGROUND_TASK_IDENTIFIER, {
        minimumInterval: MINIMUM_INTERVAL,
      });
      console.log(
        `[BEEFSYNC] Tarefa com ID: ${BACKGROUND_TASK_IDENTIFIER} registrada com sucesso.`
      );
    } else {
      console.log(
        `[BEEFSYNC] Tarefa ${BACKGROUND_TASK_IDENTIFIER} já está registrada.`
      );
    }
  } catch (error) {
    console.error("[BEEFSYNC] Falha ao registrar a tarefa de fundo.", error);
  }
};
