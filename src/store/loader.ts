import { db } from "../db";
import {
  EmployeeSchemaDB,
  FarmSchemaDB,
  FeedFoodsSchemaDB,
  FoodSchemaDB,
  PreMixFoodsSchemaDB,
} from "../db/types";
import { store } from "./tinybase";

export async function loadFarms() {
  const rows: FarmSchemaDB[] = await db.getAllAsync("SELECT * FROM farm");
  for (const row of rows) {
    store.setRow("farm", row.id.toString(), {
      secure_id: row.secure_id,
      name: row.name,
      created_at: row.created_at,
      updated_at: row.updated_at,
    });
  }
  console.log(`Loaded ${rows.length} farms into store`);
}

export async function loadEmployee() {
  const rows: EmployeeSchemaDB[] = await db.getAllAsync(
    "SELECT * FROM employee"
  );
  for (const row of rows) {
    store.setRow("employee", row.id.toString(), {
      secure_id: row.secure_id,
      function: row.function,
      name: row.name,
      farm_id: row.farm_id,
      created_at: row.created_at,
      updated_at: row.updated_at,
    });
  }

  console.log(`Loaded ${rows.length} employees into store`);
}

export async function loadFood() {
  const rows: FoodSchemaDB[] = await db.getAllAsync("SELECT * FROM food");
  for (const row of rows) {
    store.setRow("food", row.id.toString(), {
      secure_id: row.secure_id,
      name: row.name,
      percent_ms: row.percent_ms,
      price_ton: row.price_ton,
      type: row.type,
      created_at: row.created_at,
      updated_at: row.updated_at,
    });
  }

  console.log(`Loaded ${rows.length} foods into store`);
}

export async function loadFeedFoods() {
  const rows: FeedFoodsSchemaDB[] = await db.getAllAsync(
    "SELECT * FROM feed_foods"
  );
  for (const row of rows) {
    store.setRow("feed_foods", `${row.feed_food_id}-${row.food_id}`, {
      feed_food_id: row.feed_food_id,
      food_id: row.food_id,
      percent_incl_mo_food: row.percent_incl_mo_food,
      order: row.order,
      created_at: row.created_at,
      updated_at: row.updated_at,
    });
  }

  console.log(`Loaded ${rows.length} feed foods into store`);
}

export async function loadPreMixFoods() {
  const rows: PreMixFoodsSchemaDB[] = await db.getAllAsync(
    "SELECT * FROM pre_mix_foods"
  );
  for (const row of rows) {
    store.setRow("pre_mix_foods", `${row.pre_mix_food_id}-${row.food_id}`, {
      pre_mix_food_id: row.pre_mix_food_id,
      food_id: row.food_id,
      percent_incl_mo_food: row.percent_incl_mo_food,
      percent_incl_ms_food: row.percent_incl_ms_food,
      percent_ms: row.percent_ms,
      order: row.order,
      created_at: row.created_at,
      updated_at: row.updated_at,
    });
  }

  console.log(`Loaded ${rows.length} pre-mix foods into store`);
}
