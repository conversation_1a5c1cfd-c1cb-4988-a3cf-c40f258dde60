import { Buffer } from "buffer";

// Tipos base para o resultado do processamento
export interface ScaleReading {
  weight: number;
  stable: boolean;
  unit?: string;
  sign: 1 | -1;
  electronicId?: string; // Identificação eletrônica da balança (se disponível)
  outOfZero?: boolean; // Indica se a balança está fora do zero (se disponível)
}

export interface ScaleFrameExtractionResult {
  frames: Buffer[];
  rest: Buffer;
}

// Interface base para decodificadores de balança
export interface ScaleProtocolDecoder {
  // Extrai frames completos do buffer de dados brutos
  extractFrames(buffer: Buffer): ScaleFrameExtractionResult;

  // Decodifica um frame em dados de peso
  parseFrame(frame: Buffer): ScaleReading | null;

  // Configurações específicas do protocolo
  getProtocolConfig(): ScaleProtocolConfig;
}

export interface ScaleProtocolConfig {
  name: string;
  serviceUUIDs: string[];
  characteristicUUIDs: string[];
  startCommand?: string; // Comando para iniciar streaming (se necessário)
  mtuSize?: number;
}

// Enum para tipos de balança suportados
export enum ScaleType {
  KM3_N_V4 = "KM3-N-V4",
  KM3_PLUS_V3 = "KM3-PLUS-V3",
  // Adicionar novos tipos aqui conforme necessário
}

// Interface para identificação de dispositivos
export interface ScaleDeviceIdentifier {
  type: ScaleType;
  matcher: (deviceName: string, serviceUUIDs?: string[]) => boolean;
}
