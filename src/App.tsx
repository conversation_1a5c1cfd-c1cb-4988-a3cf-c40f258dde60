import "@/global.css";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import React, { useEffect, useRef } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { LoginScreen } from "./screens/auth/LoginScreen";
import { ForgotPasswordScreen } from "./screens/auth/ForgotPasswordScreen";
import { HomeScreen } from "./screens/app/home/<USER>";
import { TroughReadingScreen } from "./screens/app/home/<USER>/TroughReadingScreen";
import { Host } from "react-native-portalize";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { TreatmentRealizationsScreen } from "./screens/app/home/<USER>/TreatmentRealizationsScreen";
import { DietFabricationScreen } from "./screens/app/home/<USER>/DietFabricationScreen";
import { FeedFabricationScreen } from "./screens/app/home/<USER>/FeedFabricationScreen";
import { AnimalsEntryScreen } from "./screens/app/home/<USER>/AnimalsEntryScreen";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { SettingsScreen } from "./screens/app/home/<USER>/SettingsScreen";
import { AuthProvider, useAuth } from "@/src/contexts/AuthContext";
import { useNetwork } from "./hooks/useNetwork";
import { initDB } from "./db";
import { initializeBackgroundTask } from "./task/initialize";
import { resetSyncMetadata } from "./sync/handlers/resetSyncMetadata";
import { AppState, AppStateStatus } from "react-native";

let isInitialized = false;

const initializeApp = async () => {
  if (isInitialized) return;

  try {
    console.log("Iniciando inicialização do app...");
    if (__DEV__) {
      //   // Debugging
      //   TaskManager.getRegisteredTasksAsync().then((tasks) => {
      //     console.log(tasks);
      //   });
      //   await TaskManager.unregisterAllTasksAsync();
    }

    await initializeBackgroundTask();
    console.log("Background task inicializada");
    isInitialized = true;
  } catch (error) {
    console.error("Erro ao inicializar app:", error);
  }
};

initializeApp();

const Stack = createNativeStackNavigator();

function RootNavigator() {
  const { token, isRestoring } = useAuth();
  const isOnline = useNetwork();
  const [dbInitialized, setDbInitialized] = React.useState(false);
  const appState = useRef(AppState.currentState);

  const initializeDB = async () => {
    try {
      await initDB();
      setDbInitialized(true);
      console.log("DB inicializado com sucesso");
    } catch (error) {
      console.error("Erro ao inicializar DB:", error);
      setDbInitialized(false);
    }
  };

  useEffect(() => {
    initializeDB();

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        console.log("App voltou para foreground, reinicializando DB");
        initializeDB();
        resetSyncMetadata(); //TODO: apenas desenvolvimento
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      subscription?.remove();
    };
  }, []);

  if (isRestoring || !dbInitialized) {
    return null;
  }

  return (
    <Stack.Navigator
      screenOptions={{ headerShown: false }}
      id={undefined}
      initialRouteName={token ? "Home" : "Login"}
    >
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="TroughReading" component={TroughReadingScreen} />
      <Stack.Screen
        name="TreatmentRealizations"
        component={TreatmentRealizationsScreen}
      />
      <Stack.Screen name="DietFabrication" component={DietFabricationScreen} />
      <Stack.Screen name="FeedFabrication" component={FeedFabricationScreen} />
      <Stack.Screen name="AnimalsEntry" component={AnimalsEntryScreen} />
      <Stack.Screen name="Settings" component={SettingsScreen} />
    </Stack.Navigator>
  );
}

export default function App() {
  return (
    <SafeAreaProvider>
      <AuthProvider>
        <GluestackUIProvider mode="light">
          <NavigationContainer>
            <GestureHandlerRootView style={{ flex: 1 }}>
              <Host>
                <RootNavigator />
              </Host>
            </GestureHandlerRootView>
          </NavigationContainer>
        </GluestackUIProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}
