import { HStack } from "@/components/ui/hstack";
import { Icon, MenuIcon } from "@/components/ui/icon";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import LogoHeader from "@/src/assets/logo-azul.svg";
import { useState } from "react";
import { TouchableOpacity, View } from "react-native";
import {
  Drawer,
  DrawerBackdrop,
  DrawerContent,
  DrawerHeader,
  DrawerBody,
  DrawerFooter,
  DrawerCloseButton,
} from '@/components/ui/drawer';
import { Divider } from "@/components/ui/divider";
import { Pressable } from '@/components/ui/pressable';
import { User, LogOut, SettingsIcon, ChevronLeft } from 'lucide-react-native';
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";

interface HeaderProps {
  isHome?: boolean;
  userName?: string;
  title?: string;
  farmName: string;
}

export function Header({ userName, farmName, title, isHome = false }: HeaderProps) {
  const navigation = useNavigation();
  const [showDrawer, setShowDrawer] = useState(false);
  const handleMenuPress = () => {
    setShowDrawer(!showDrawer);
  };

  const handleLogout = () => {
    // Provisório
    navigation.navigate("Login" as never);
    setShowDrawer(false);
  };

  return (
    <HStack
      className="
        w-full
        bg-secondary-100
        border-[1px]
        border-[#bfbfbf]
        rounded-b-3xl
        justify-between
        items-center
        px-6
        py-3
      "
    >
      <HStack className="gap-4 items-center">
        <TouchableOpacity onPress={handleMenuPress} activeOpacity={0.7}>
          <Icon as={MenuIcon} className="w-10 h-10 color-primary-500" />
        </TouchableOpacity>
        <VStack className="gap-2">
          <Text className="text-[20px] font-medium text-primary-900">
            {isHome ? `Olá, ${userName}!` : title}
          </Text>
          <Text className="text-[16px] text-primary-300">{farmName}</Text>
        </VStack>
      </HStack>
      <LogoHeader width={78} height={74} />
      <Drawer
        isOpen={showDrawer}
        onClose={() => {
          setShowDrawer(false);
        }}
      >
        <DrawerBackdrop />
        <SafeAreaView className="flex-1">
          <View className="flex-1 w-[70%]">
            <DrawerContent className="w-full md:w-[300px] bg-primary-800 p-2">
              <DrawerHeader className="relative justify-center py-2"> {/* 1. Adicionado "relative" e ajustado o padding */}
                <DrawerCloseButton className="absolute top-2 right-0">
                  <Icon as={ChevronLeft} size={"xl"} className="color-white" />
                </DrawerCloseButton>
                <VStack className="justify-center items-center">
                  <Text size="lg" className="text-xl font-medium text-white">
                    {userName}
                  </Text>
                  <Text size="lg" className="text-typography-300">
                    {farmName}
                  </Text>
                </VStack>
              </DrawerHeader>
              <Divider className="my-4" />
              <DrawerBody contentContainerClassName="gap-2">
                <Pressable className="gap-3 flex-row items-center hover:bg-background-50 p-2 rounded-md">
                  <Icon as={User} color="white" size="lg" className="text-typography-600" />
                  <Text size={"lg"} className="text-white">Perfil</Text>
                </Pressable>
                <Pressable onPress={() => navigation.navigate("Settings" as never)} className="gap-3 flex-row items-center hover:bg-background-50 p-2 rounded-md">
                  <Icon as={SettingsIcon} color="white" size="lg" className="text-typography-600" />
                  <Text size={"lg"} className="text-white">Configurações</Text>
                </Pressable>
              </DrawerBody>
              <DrawerFooter className="justify-start">
                <Pressable onPress={handleLogout} className="gap-3 flex-row items-center hover:bg-background-50 p-2 rounded-md">
                  <Icon as={LogOut} color="white" size="lg" className="text-typography-600" />
                  <Text size={"lg"} className="text-white">Sair</Text>
                </Pressable>
              </DrawerFooter>
            </DrawerContent>
          </View>
        </SafeAreaView>
      </Drawer>
    </HStack>
  );
}
