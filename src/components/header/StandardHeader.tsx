import { Box } from "@/components/ui/box";
import { HStack } from "@/components/ui/hstack";
import { Icon } from "@/components/ui/icon";
import { Text } from "@/components/ui/text";
import LogoHeader from "@/src/assets/logo-azul.svg";
import { useNavigation } from "@react-navigation/native";
import { ArrowLeftIcon } from "lucide-react-native";
import { TouchableOpacity } from "react-native";

interface StandardHeaderProps {
  title?: string;
}

export function StandardHeader({ title }: StandardHeaderProps) {
  const navigation = useNavigation();

  return (
    <HStack
      className="
        w-full
        bg-secondary-100
        border-[1px]
        border-[#bfbfbf]
        rounded-b-3xl
        justify-between
        items-center
        p-3
      "
    >
      <TouchableOpacity onPress={() => navigation.goBack()} activeOpacity={0.6}>
        <Box
          className="
            w-14
            h-14
            rounded-full
            border-[1px]
            border-[#bfbfbf]
            bg-white
            justify-center
            items-center
          "
        >
          <Icon className="w-10 h-10 color-primary-500" as={ArrowLeftIcon} />
        </Box>
      </TouchableOpacity>
      <Text className="text-[22px] text-center font-medium text-primary-900">{title}</Text>
      <LogoHeader width={78} height={74} />
    </HStack>
  );
}
