// src/components/treatmentRealizations/TreatmentSelector.tsx
import { useState } from "react";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Box } from "@/components/ui/box";
import { Pressable } from "react-native";

interface TreatmentSelectorProps {
  treatments?: number[];
}

export function TreatmentSelector({ treatments = [1, 2, 3, 4] }: TreatmentSelectorProps) {
  const [selected, setSelected] = useState<number | null>(null);

  return (
    <VStack className="gap-2">
      <Text className="text-2xl font-medium text-primary-500">Trato</Text>
      <HStack className="gap-2">
        {treatments.map((item) => (
          <Pressable key={item} onPress={() => setSelected(item)}>
            <Box
              className={`w-16 h-16 rounded-2xl border-[1px] justify-center items-center ${
                selected === item
                  ? "border-primary-500 bg-primary-100"
                  : "border-[#bfbfbf] bg-white"
              }`}
            >
              <Text className="text-2xl">{item}</Text>
            </Box>
          </Pressable>
        ))}
      </HStack>
    </VStack>
  );
}