import { StyleSheet } from "react-native";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { Box } from "@/components/ui/box";
import { LinearGradient } from "expo-linear-gradient";

interface RealizedQuantityDisplayProps {
  quantity: number;
}

export function RealizedQuantityDisplay({ quantity }: RealizedQuantityDisplayProps) {
  return (
    <VStack className="gap-2">
      <Text className="text-3xl font-medium text-primary-500">
        Qtd. Realizada
      </Text>
      <Box className="w-full h-32 rounded-2xl border-[1px] border-[#bfbfbf] justify-center items-center">
        <LinearGradient
          colors={["rgb(238,238,238)", "rgb(238,238,238)", "rgb(25,254,33)"]}
          locations={[0, 0.4, 1]}
          style={[StyleSheet.absoluteFill, { borderRadius: 12 }]}
        />
        <Text className="text-7xl pt-4 text-primary-500">{quantity}</Text>
      </Box>
    </VStack>
  );
}