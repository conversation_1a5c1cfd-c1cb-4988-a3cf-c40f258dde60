import { FormControl, FormControlLabel, FormControlLabelText } from "@/components/ui/form-control";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import { CalendarIcon } from "lucide-react-native";

interface DateSelectorInputProps {
  value: string;
  onPress: () => void;
  label?: string;
  placeholder?: string;
}

export function DatePickerInput({ value, onPress, label="Data", placeholder="Selecione a data" }: DateSelectorInputProps) {
  return (
    <FormControl className="w-full gap-2">
      <FormControlLabel>
        <FormControlLabelText className="text-2xl font-semibold text-primary-500">
          {label}
        </FormControlLabelText>
      </FormControlLabel>
      <Input className="w-full bg-white rounded-xl h-16">
        <InputField
          className="text-2xl px-4"
          placeholder={placeholder}
          editable={false}
          onPressIn={onPress}
          value={value}
        />
        <InputSlot className="p-3" onPress={onPress}>
          <InputIcon
            className="text-typography-600 w-8 h-8"
            as={CalendarIcon}
          />
        </InputSlot>
      </Input>
    </FormControl>
  );
}