import { Portal } from "react-native-portalize";
import { Modalize } from "react-native-modalize";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import DateTimePicker from "@react-native-community/datetimepicker";
import { IHandles } from "react-native-modalize/lib/options";

interface DatePickerModalProps {
  modalizeRef: React.RefObject<IHandles>;
  selectedDate: Date;
  onDateChange: (event: any, date?: Date) => void;
  onConfirm: () => void;
}

export const DatePickerModal: React.FC<DatePickerModalProps> = ({
  modalizeRef,
  selectedDate,
  onDateChange,
  onConfirm,
}) => {
  return (
    <Portal>
      <Modalize
        ref={modalizeRef}
        modalHeight={400}
        panGestureEnabled={false}
        handlePosition="inside"
        handleStyle={{ backgroundColor: "#d1d5db" }}
      >
        <VStack className="flex-1 w-full items-center p-6">
          <Text className="text-2xl font-medium text-center text-primary-500 mb-4">
            Selecione a Data
          </Text>
          <DateTimePicker
            mode="date"
            display="spinner"
            value={selectedDate}
            onChange={onDateChange}
            locale="pt-BR"
          />
          <Button
            onPress={onConfirm}
            className="w-[80%] bg-primary-500 shadow-sm rounded-xl h-16 mt-6 mb-4"
          >
            <ButtonText className="text-white text-2xl">Selecionar</ButtonText>
          </Button>
        </VStack>
      </Modalize>
    </Portal>
  );
};
