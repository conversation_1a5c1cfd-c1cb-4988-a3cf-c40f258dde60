import React from "react";
import { FormSelect } from "./FormSelect";

interface Employee {
  label: string;
  value: string;
  function?: string;
  secure_id?: string;
}

interface SimpleEmployeeSelectProps {
  label: string;
  employees: Employee[];
  onValueChange: (value: string) => void;
  placeholder?: string;
  loading?: boolean;
}

export function SimpleEmployeeSelect({
  label,
  employees,
  onValueChange,
  placeholder = "Selecione um funcionário",
  loading = false,
}: SimpleEmployeeSelectProps) {
  const employeeItems = employees.map((employee) => ({
    label: employee.function || employee.label,
    value: employee.value,
  }));

  const items = loading
    ? [{ label: "Carregando...", value: "" }]
    : employeeItems;

  return (
    <FormSelect
      label={label}
      items={items}
      onValueChange={onValueChange}
      placeholder={placeholder}
    />
  );
}
