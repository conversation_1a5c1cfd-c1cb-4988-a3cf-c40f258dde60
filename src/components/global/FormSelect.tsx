import { FormControl, FormControlLabel, FormControlLabelText } from "@/components/ui/form-control";
import { Select, SelectBackdrop, SelectContent, SelectDragIndicator, SelectDragIndicatorWrapper, SelectIcon, SelectInput, SelectItem, SelectPortal, SelectTrigger } from "@/components/ui/select";
import { ChevronDownIcon } from "lucide-react-native";

interface FormSelectProps {
  label: string;
  items: { label: string; value: string }[];
  onValueChange: (value: string) => void;
  placeholder?: string;
}

export function FormSelect({ label, items, onValueChange, placeholder }: FormSelectProps) {
  return (
    <FormControl className="w-full gap-2">
      <FormControlLabel>
        <FormControlLabelText className="text-2xl font-semibold text-primary-500">
          {label}
        </FormControlLabelText>
      </FormControlLabel>
      <Select className="bg-white h-16 rounded-xl" onValueChange={onValueChange}>
        <SelectTrigger className="w-full h-16 justify-between rounded-xl" variant="outline" size="md">
          <SelectInput className="text-2xl" placeholder={placeholder || "Selecione..."} />
          <SelectIcon className="mr-3" as={ChevronDownIcon} />
        </SelectTrigger>
        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            {items.map((item) => (
              <SelectItem key={item.value} label={item.label} value={item.value} />
            ))}
          </SelectContent>
        </SelectPortal>
      </Select>
    </FormControl>
  );
}
