import { Box } from "@/components/ui/box";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { TouchableOpacity } from "react-native";

interface ActionButtonsProps {
  onPressCancel: () => void;
  onPressConfirm: () => void;
  cancelLabel?: string;
  confirmLabel?: string;
  confirmButtonProps?: any;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  onPressCancel,
  onPressConfirm,
  cancelLabel = "CANCELAR",
  confirmLabel = "CONFIRMAR",
  confirmButtonProps,
}) => (
  <HStack className="gap-4 my-4">
    <TouchableOpacity onPress={onPressCancel} className="w-[48%]">
      <Box className="h-20 rounded-2xl border-2 border-primary-500 bg-white justify-center items-center">
        <Text className="text-2xl text-primary-500 font-bold">{cancelLabel}</Text>
      </Box>
    </TouchableOpacity>
    <TouchableOpacity onPress={onPressConfirm} className="w-[48%]">
      <Box {...confirmButtonProps} className="h-20 rounded-2xl border-2 border-primary-500 bg-primary-500 justify-center items-center">
        <Text className="text-2xl text-white font-bold">{confirmLabel}</Text>
      </Box>
    </TouchableOpacity>
  </HStack>
);
