import React, { useState, useMemo } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  Modal as RNModal,
} from "react-native";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import {
  ChevronDownIcon,
  ChevronUpIcon,
  SearchIcon,
  XIcon,
} from "lucide-react-native";

interface Employee {
  label: string;
  value: string;
  function?: string;
  secure_id?: string;
}

interface EmployeeComboboxProps {
  label: string;
  employees: Employee[];
  onValueChange: (value: string) => void;
  placeholder?: string;
  loading?: boolean;
  selectedValue?: string;
}

export function EmployeeCombobox({
  label,
  employees,
  onValueChange,
  placeholder = "Selecione um funcionário",
  loading = false,
  selectedValue,
}: EmployeeComboboxProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState("");

  const filteredEmployees = useMemo(() => {
    if (!searchText.trim()) return employees;

    return employees.filter(
      (employee) =>
        employee.label.toLowerCase().includes(searchText.toLowerCase()) ||
        (employee.function &&
          employee.function.toLowerCase().includes(searchText.toLowerCase()))
    );
  }, [employees, searchText]);

  const selectedEmployee = employees.find((emp) => emp.value === selectedValue);

  const handleSelectEmployee = (employee: Employee) => {
    onValueChange(employee.value);
    setIsOpen(false);
    setSearchText("");
  };

  const openModal = () => {
    setIsOpen(true);
    setSearchText("");
  };

  const closeModal = () => {
    setIsOpen(false);
    setSearchText("");
  };

  const renderEmployeeItem = ({ item }: { item: Employee }) => (
    <TouchableOpacity
      className="p-4 border-b border-gray-200 bg-white"
      onPress={() => handleSelectEmployee(item)}
    >
      <VStack className="gap-1">
        <Text className="text-lg font-medium text-gray-900">{item.label}</Text>
        {item.function && (
          <Text className="text-sm text-gray-600">Função: {item.function}</Text>
        )}
      </VStack>
    </TouchableOpacity>
  );

  return (
    <>
      <FormControl className="w-full gap-2">
        <FormControlLabel>
          <FormControlLabelText className="text-2xl font-semibold text-primary-500">
            {label}
          </FormControlLabelText>
        </FormControlLabel>

        <TouchableOpacity
          className="bg-white h-16 rounded-xl border border-gray-300 px-4 justify-between items-center flex-row"
          onPress={openModal}
          disabled={loading}
        >
          <Text
            className={`text-2xl ${
              selectedEmployee ? "text-gray-900" : "text-gray-500"
            }`}
            numberOfLines={1}
            style={{ flex: 1 }}
          >
            {loading ? "Carregando..." : selectedEmployee?.label || placeholder}
          </Text>

          <ChevronDownIcon size={24} color="#6B7280" />
        </TouchableOpacity>
      </FormControl>

      {/* Modal para seleção de funcionários */}
      <RNModal
        visible={isOpen}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={closeModal}
      >
        <View className="flex-1 bg-gray-50">
          {/* Header do modal */}
          <View className="bg-white p-4 border-b border-gray-200">
            <HStack className="justify-between items-center">
              <Text className="text-xl font-semibold text-gray-900">
                Selecionar Funcionário
              </Text>
              <TouchableOpacity onPress={closeModal}>
                <XIcon size={24} color="#6B7280" />
              </TouchableOpacity>
            </HStack>
          </View>

          {/* Campo de busca */}
          <View className="p-4 bg-white border-b border-gray-200">
            <HStack className="items-center bg-gray-100 rounded-lg px-3 py-3 gap-2">
              <SearchIcon size={20} color="#6B7280" />
              <TextInput
                className="flex-1 text-lg"
                placeholder="Buscar funcionário..."
                value={searchText}
                onChangeText={setSearchText}
                autoFocus
              />
            </HStack>
          </View>

          {/* Lista de funcionários */}
          <FlatList
            data={filteredEmployees}
            renderItem={renderEmployeeItem}
            keyExtractor={(item) => item.value}
            contentContainerStyle={{ paddingVertical: 8 }}
            ListEmptyComponent={
              <View className="p-8">
                <Text className="text-center text-gray-500 text-lg">
                  {searchText
                    ? "Nenhum funcionário encontrado"
                    : "Nenhum funcionário disponível"}
                </Text>
              </View>
            }
          />
        </View>
      </RNModal>
    </>
  );
}
