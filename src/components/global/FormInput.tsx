import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { Input, InputField } from "@/components/ui/input";
import { Text } from "@/components/ui/text";
import { ComponentProps } from "react";

// Permite passar todas as propriedades de um InputField, como placeholder, value, onChangeText, etc.
interface FormInputProps extends ComponentProps<typeof InputField> {
  label: string;
  suffix?: string;
}

export function FormInput({ label, suffix, ...inputProps }: FormInputProps) {
  return (
    <FormControl className="w-full gap-2">
      <FormControlLabel>
        <FormControlLabelText className="text-2xl font-semibold text-primary-500">
          {label}
        </FormControlLabelText>
      </FormControlLabel>
      <Input className="w-full bg-white rounded-xl h-16">
        <InputField className="text-2xl px-4" {...inputProps} />
        {suffix && <Text className="text-2xl px-4">{suffix}</Text>}
      </Input>
    </FormControl>
  );
}