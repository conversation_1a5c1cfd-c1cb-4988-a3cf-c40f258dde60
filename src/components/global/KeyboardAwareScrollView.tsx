import React, { useRef, useEffect, useState } from 'react';
import {
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  View,
} from 'react-native';
import { ScrollViewProps } from 'react-native';

interface KeyboardAwareScrollViewProps extends ScrollViewProps {
  children: React.ReactNode;
  extraScrollHeight?: number;
  keyboardShouldPersistTaps?: 'always' | 'never' | 'handled';
  resetScrollOnKeyboardHide?: boolean;
}

export const KeyboardAwareScrollView: React.FC<KeyboardAwareScrollViewProps> = ({
  children,
  extraScrollHeight = 100,
  keyboardShouldPersistTaps = 'handled',
  resetScrollOnKeyboardHide = false,
  ...scrollViewProps
}) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const originalScrollPosition = useRef({ x: 0, y: 0 });

  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      handleKeyboardShow
    );
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      handleKeyboardHide
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  const handleKeyboardShow = () => {
    setIsKeyboardVisible(true);
  };

  const handleKeyboardHide = () => {
    setIsKeyboardVisible(false);

    if (resetScrollOnKeyboardHide) {
      setTimeout(() => {
        scrollViewRef.current?.scrollTo({
          ...originalScrollPosition.current,
          animated: true,
        });
      }, 100);
    }
  };

  const handleScroll = (event: any) => {
    if (!isKeyboardVisible) {
      originalScrollPosition.current = {
        x: event.nativeEvent.contentOffset.x,
        y: event.nativeEvent.contentOffset.y,
      };
    }

    if (scrollViewProps.onScroll) {
      scrollViewProps.onScroll(event);
    }
  };

  const KeyboardAvoidingComponent = Platform.OS === 'ios' ? KeyboardAvoidingView : View;
  const keyboardAvoidingProps = Platform.OS === 'ios'
    ? {
        behavior: 'padding' as const,
        keyboardVerticalOffset: Platform.OS === 'ios' ? 0 : 0
      }
    : {};

  return (
    <KeyboardAvoidingComponent style={{ flex: 1 }} {...keyboardAvoidingProps}>
      <ScrollView
        ref={scrollViewRef}
        keyboardShouldPersistTaps={keyboardShouldPersistTaps}
        showsVerticalScrollIndicator={true}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        {...scrollViewProps}
      >
        {children}
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
};
