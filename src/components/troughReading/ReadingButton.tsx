import { Box } from "@/components/ui/box";
import { Text } from "@/components/ui/text";
import { TouchableOpacity } from "react-native";

interface ReadingButtonProps {
  label: string;
  isSelected: boolean;
  onPress: () => void;
  widthClass: string;
}

export const ReadingButton: React.FC<ReadingButtonProps> = ({
  label,
  isSelected,
  onPress,
  widthClass,
}) => {
  const containerClasses = isSelected
    ? "bg-primary-500 border-primary-500"
    : "bg-white border-[#bfbfbf]";
  const textClasses = isSelected ? "text-white" : "text-black";

  return (
    <TouchableOpacity onPress={onPress} className={widthClass}>
      <Box
        className={`h-24 rounded-2xl border-[1px] justify-center items-center ${containerClasses}`}
      >
        <Text className={`text-2xl font-normal ${textClasses}`}>{label}</Text>
      </Box>
    </TouchableOpacity>
  );
};
