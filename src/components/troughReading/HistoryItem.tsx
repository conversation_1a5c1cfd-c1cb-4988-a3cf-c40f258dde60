import { Box } from "@/components/ui/box";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";

interface HistoryItemProps {
  day: number;
  value: string;
}

export const HistoryItem: React.FC<HistoryItemProps> = ({ day, value }) => (
  <VStack className="items-center gap-1">
    <Text className="text-2xl text-primary-500">Dia {day}</Text>
    <Box className="w-16 h-16 rounded-2xl border-[1px] border-[#bfbfbf] bg-white justify-center items-center">
      <Text className="text-2xl">{value}</Text>
    </Box>
  </VStack>
);
