import { TouchableOpacity } from "react-native";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";

interface MenuCardProps {
  icon: React.ReactNode; // Icon
  label: string;
  onPress?: () => void;
}

export function MenuCard({ icon, label, onPress }: MenuCardProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      className="
        w-[48%]
        bg-white
        border-[1px]
        border-[#bfbfbf]
        rounded-3xl
        items-center
        justify-center
        p-4
      "
      activeOpacity={0.8}
    >
      <VStack className="items-center justify-center">
        {icon}
        <Text className="mt-2 text-2xl text-center font-semibold text-primary-500">
          {label}
        </Text>
      </VStack>
    </TouchableOpacity>
  );
}
