import { View } from "react-native";
import { MenuCard } from "@/src/components/cards/MenuCards";
import LeituraDeCochoIcon from "@/src/assets/icons/leituraDeCochoIcon.svg";
import FabDietaIcon from "@/src/assets/icons/fabDietaIcon.svg";
import FabMisturaRacaoIcon from "@/src/assets/icons/fabMisturaRacaoIcon.svg";
import RealizacaoDeTratoIcon from "@/src/assets/icons/realizacaoDeTratoIcon.svg";
import EntradaDeAnimaisIcon from "@/src/assets/icons/entradaDeAnimaisIcon.svg";
import { useNavigation } from "@react-navigation/native";

export function MainMenu() {
  const navigation = useNavigation();
  return (
    <View className="flex-row flex-wrap gap-4">
      <MenuCard
        icon={<LeituraDeCochoIcon width={42} height={42} />}
        label="Leitura de Cocho"
        onPress={() => navigation.navigate("TroughReading" as never)}
      />
      <MenuCard
        icon={<FabDietaIcon width={42} height={42} />}
        label="Fab. de Dieta"
        onPress={() => navigation.navigate("DietFabrication" as never)}
      />
      <MenuCard
        icon={<RealizacaoDeTratoIcon width={42} height={42} />}
        label="Realizações de Trato"
        onPress={() => navigation.navigate("TreatmentRealizations" as never)}
      />
      <MenuCard
        icon={<FabMisturaRacaoIcon width={42} height={42} />}
        label="Fab. de Mistura/Ração"
        onPress={() => navigation.navigate("FeedFabrication" as never)}
      />
      <MenuCard
        icon={<EntradaDeAnimaisIcon width={42} height={42} />}
        label="Entrada de Animais"
        onPress={() => navigation.navigate("AnimalsEntry" as never)}
      />
    </View>
  );
}
