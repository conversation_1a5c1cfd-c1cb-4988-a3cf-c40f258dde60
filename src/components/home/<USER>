import { Box } from "@/components/ui/box";
import { Text } from "@/components/ui/text";
import { Pressable } from "react-native";

interface SyncAlertProps {
  onPress?: () => void;
}

export function SyncAlert({ onPress }: SyncAlertProps) {
  return (
    <Pressable onPress={onPress}>
      <Box className="w-full bg-[#FFD20C] border-[1px] border-[#B49300] rounded-xl">
        <Text className="text-xl font-semibold text-center">
          {`Existem dados para serem sincronizados\nSincronizar agora`}
        </Text>
      </Box>
    </Pressable>
  );
}
