export const schema = `
CREATE TABLE IF NOT EXISTS sync_queue (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  entity_type TEXT NOT NULL,
  operation TEXT NOT NULL,
  payload TEXT NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  status TEXT DEFAULT 'pending'
);

CREATE TABLE IF NOT EXISTS sync_metadata (
  entity_type TEXT PRIMARY KEY,
  last_sync_at TEXT
);

CREATE TABLE IF NOT EXISTS farm (
  id INTEGER PRIMARY KEY,
  secure_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS employee(
  id INTEGER PRIMARY KEY,
  secure_id TEXT UNIQUE NOT NULL,
  function TEXT NOT NULL,
  name TEXT NOT NULL,
  farm_id INTEGER NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  <PERSON>OR<PERSON><PERSON>N KEY (farm_id) REFERENCES farm(id)
);

CREATE TABLE IF NOT EXISTS food (
  id INTEGER PRIMARY KEY,
  secure_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  percent_ms FLOAT NOT NULL,
  price_ton REAL NOT NULL,
  type TEXT CHECK(type IN ('feed', 'food', 'mix')) NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS feed_foods (
  feed_food_id INTEGER NOT NULL,
  food_id INTEGER NOT NULL,
  percent_incl_mo_food FLOAT NOT NULL,
  "order" INTEGER NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (feed_food_id, food_id),
  FOREIGN KEY (feed_food_id) REFERENCES food(id) ON DELETE CASCADE,
  FOREIGN KEY (food_id) REFERENCES food(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS pre_mix_foods (
  pre_mix_food_id INTEGER NOT NULL,
  food_id INTEGER NOT NULL,
  percent_incl_mo_food FLOAT NOT NULL,
  percent_incl_ms_food FLOAT NOT NULL,
  percent_ms FLOAT NOT NULL,
  "order" INTEGER NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (pre_mix_food_id, food_id),
  FOREIGN KEY (pre_mix_food_id) REFERENCES food(id) ON DELETE CASCADE,
  FOREIGN KEY (food_id) REFERENCES food(id) ON DELETE CASCADE
);
`;
