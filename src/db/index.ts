import * as SQLite from "expo-sqlite";
import { schema } from "./schema";

let db: SQLite.SQLiteDatabase | null = null;

export function getDB() {
  if (!db) {
    db = SQLite.openDatabaseSync("app.db");
  }
  return db;
}

export async function initDB() {
  try {
    console.log("Inicializando banco de dados...");

    if (!db) {
      db = SQLite.openDatabaseSync("app.db");
    }

    await db.execAsync(schema);
  } catch (error) {
    if (error.message?.includes("closed resource")) {
      db = SQLite.openDatabaseSync("app.db");
      await db.execAsync(schema);
    } else {
      throw error;
    }
  }
}

export async function closeDB() {
  try {
    if (db) {
      await db.closeAsync();
      db = null;
    }
  } catch (error) {
    console.error("Erro ao fechar conexão com o banco de dados:", error);
    db = null;
    throw error;
  }
}
export { db };
