export class EmployeeSchemaDB {
  id: number;
  secure_id: string;
  function: string;
  name: string;
  farm_id: number;
  created_at: string;
  updated_at: string;
}

export class FarmSchemaDB {
  id: number;
  secure_id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export class FoodSchemaDB {
  id: number;
  secure_id: string;
  name: string;
  percent_ms: number;
  price_ton: number;
  type: "feed" | "food" | "mix";
  created_at: string;
  updated_at: string;
}

export class FeedFoodsSchemaDB {
  feed_food_id: number;
  food_id: number;
  percent_incl_mo_food: number;
  order: number;
  created_at: string;
  updated_at: string;
}

export class PreMixFoodsSchemaDB {
  pre_mix_food_id: number;
  food_id: number;
  percent_incl_mo_food: number;
  percent_incl_ms_food: number;
  percent_ms: number;
  order: number;
  created_at: string;
  updated_at: string;
}

export class SyncMetadataSchemaDB {
  entity_type: string;
  last_sync_at: string;
}
