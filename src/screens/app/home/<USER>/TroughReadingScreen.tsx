import { Platform, SafeAreaView, useWindowDimensions, View } from "react-native";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { useRef, useState } from "react";
import { DatePickerModal } from "@/src/components/global/DatePickerModal";
import { Modalize } from "react-native-modalize";
import { HistoryItem } from "@/src/components/troughReading/HistoryItem";
import { ReadingButton } from "@/src/components/troughReading/ReadingButton";
import { ActionButtons } from "@/src/components/global/ActionButtons";
import { StandardHeader } from "@/src/components/header/StandardHeader";
import { DatePickerInput } from "@/src/components/global/DatePickerInput";
import DateTimePicker from "@react-native-community/datetimepicker";
import { KeyboardAwareScrollView } from "@/src/components/global/KeyboardAwareScrollView";

export function TroughReadingScreen() {
  const modalizeRef = useRef<Modalize>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [displayDate, setDisplayDate] = useState("");
  const [selectedReading, setSelectedReading] = useState<string | null>(null);

  const [showAndroidPicker, setShowAndroidPicker] = useState(false);

  const { width, height } = useWindowDimensions();
  const isLandscape = width > height;

  const historyData = [
    { day: 1, value: "0" },
    { day: 2, value: "2" },
    { day: 3, value: "REP" },
  ];
  const readingActions = ["-2", "-1", "0", "1", "2", "Replicar"];

  const openDatePicker = () => {
    if (Platform.OS === "android") {
      setShowAndroidPicker(true);
    } else {
      modalizeRef.current?.open();
    }
  };

  const onDateChange = (event, date) => {
    if (Platform.OS === "android") {
      setShowAndroidPicker(false);
    }

    if (event.type === "set" && date) {
      setSelectedDate(date);
      if (Platform.OS === "android") {
        setDisplayDate(date.toLocaleDateString("pt-BR"));
      }
    }
  };

  const handleReadingSelect = (label: string) => {
    setSelectedReading((prev) => (prev === label ? null : label));
  };

  const handleConfirmDateIOS = () => {
    setDisplayDate(selectedDate.toLocaleDateString("pt-BR"));
    modalizeRef.current?.close();
  };

  const readingButtonWidthClass = isLandscape ? "w-[32%]" : "w-[48%]";

  const LeftContent = () => (
    <VStack className="gap-4">
      <DatePickerInput value={displayDate} onPress={openDatePicker} />
      <VStack>
        <Text className="text-2xl font-medium text-primary-500">Piquete</Text>
        <Text className="width-full text-center text-4xl">Piquete 01</Text>
      </VStack>
      <VStack className="gap-2">
        <Text className="text-2xl font-medium text-primary-500">Histórico</Text>
        <HStack className="gap-2">
          {historyData.map((item) => (
            <HistoryItem key={item.day} day={item.day} value={item.value} />
          ))}
        </HStack>
      </VStack>
    </VStack>
  );

  const RightContent = () => (
    <VStack className="gap-4 flex-1 justify-between">
      <VStack className="gap-2">
        <Text className="text-2xl font-medium text-primary-500">Leitura</Text>
        <View className="flex-row flex-wrap gap-2 justify-between">
          {readingActions.map((label) => (
            <ReadingButton
              key={label}
              label={label}
              isSelected={selectedReading === label}
              onPress={() => handleReadingSelect(label)}
              widthClass={readingButtonWidthClass}
            />
          ))}
        </View>
      </VStack>
      <ActionButtons
        onPressCancel={() => console.log("Cancel")}
        onPressConfirm={() => console.log("Next")}
      />
    </VStack>
  );

  return (
    <SafeAreaView className="flex-1 bg-secondary-200">
      <StandardHeader title="Leitura de Cocho" />

      <KeyboardAwareScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        resetScrollOnKeyboardHide={true}
      >
        <View className="flex-1 p-6">
          {isLandscape ? (
            <HStack className="gap-6">
              <View className="w-[40%]">
                <LeftContent />
              </View>
              <View className="w-[60%]">
                <RightContent />
              </View>
            </HStack>
          ) : (
            <VStack className="gap-8 flex-1">
              <LeftContent />
              <RightContent />
            </VStack>
          )}
        </View>
      </KeyboardAwareScrollView>

      {showAndroidPicker && (
        <DateTimePicker
          mode="date"
          display="default" // 'default' abre o diálogo padrão do Android
          value={selectedDate}
          onChange={onDateChange}
          locale="pt-BR"
        />
      )}

      <DatePickerModal
        modalizeRef={modalizeRef}
        selectedDate={selectedDate}
        onDateChange={onDateChange}
        onConfirm={handleConfirmDateIOS}
      />
    </SafeAreaView>
  );
}
