import { SafeAreaView, View } from "react-native";
import { Header } from "@/src/components/header/Header";
import { SyncAlert } from "@/src/components/home/<USER>";
import { MainMenu } from "@/src/components/home/<USER>";
import * as BackgroundTask from "expo-background-task";

export function HomeScreen() {
  // Dados provisórios
  const user = {
    name: "<PERSON>",
    farm: "Fazenda BeefSystem",
  };

  const handleSync = async () => {
    await BackgroundTask.triggerTaskWorkerForTestingAsync();
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-200">
      <Header isHome={true} userName={user.name} farmName={user.farm} />

      <View className="flex-1 p-6 gap-8">
        <SyncAlert onPress={handleSync} />
        <MainMenu />
      </View>
    </SafeAreaView>
  );
}
