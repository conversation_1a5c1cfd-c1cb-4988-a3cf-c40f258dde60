import { Platform, SafeAreaView, View } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { useRef, useState } from "react";
import { DatePickerModal } from "@/src/components/global/DatePickerModal";
import { Modalize } from "react-native-modalize";
import { ActionButtons } from "@/src/components/global/ActionButtons";
import { StandardHeader } from "@/src/components/header/StandardHeader";
import DateTimePicker from "@react-native-community/datetimepicker";
import { KeyboardAwareScrollView } from "@/src/components/global/KeyboardAwareScrollView";

export function AnimalsEntryScreen() {
  const modalizeRef = useRef<Modalize>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [displayDate, setDisplayDate] = useState("");
  const [showAndroidPicker, setShowAndroidPicker] = useState(false);

  const openDatePicker = () => {
    if (Platform.OS === "android") {
      setShowAndroidPicker(true);
    } else {
      modalizeRef.current?.open();
    }
  };

  const onDateChange = (event, date) => {
    if (Platform.OS === "android") {
      setShowAndroidPicker(false);
    }

    if (event.type === "set" && date) {
      setSelectedDate(date);
      if (Platform.OS === "android") {
        setDisplayDate(date.toLocaleDateString("pt-BR"));
      }
    }
  };

  const handleConfirmDateIOS = () => {
    setDisplayDate(selectedDate.toLocaleDateString("pt-BR"));
    modalizeRef.current?.close();
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-200">
      <StandardHeader title="Entrada de Animais" />

      <KeyboardAwareScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        resetScrollOnKeyboardHide={true}
      >
        <View className="flex-1 p-6 justify-between">
          <VStack className="gap-4">
            
          </VStack>

          <ActionButtons
            onPressCancel={() => console.log("Cancel")}
            onPressConfirm={() => console.log("Next")}
          />
        </View>
      </KeyboardAwareScrollView>

      {showAndroidPicker && (
        <DateTimePicker
          mode="date"
          display="default"
          value={selectedDate}
          onChange={onDateChange}
          locale="pt-BR"
        />
      )}

      <DatePickerModal
        modalizeRef={modalizeRef}
        selectedDate={selectedDate}
        onDateChange={onDateChange}
        onConfirm={handleConfirmDateIOS}
      />
    </SafeAreaView>
  );
}