import { Platform, SafeAreaView, View } from "react-native";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { useRef, useState } from "react";
import { DatePickerModal } from "@/src/components/global/DatePickerModal";
import { Modalize } from "react-native-modalize";
import { ActionButtons } from "@/src/components/global/ActionButtons";
import { StandardHeader } from "@/src/components/header/StandardHeader";
import { DatePickerInput } from "@/src/components/global/DatePickerInput";
import { TreatmentSelector } from "@/src/components/treatmentRealizations/TreatmentSelector";
import { RealizedQuantityDisplay } from "@/src/components/treatmentRealizations/RealizedQuantityDisplay";
import DateTimePicker from "@react-native-community/datetimepicker";
import { KeyboardAwareScrollView } from "@/src/components/global/KeyboardAwareScrollView";

export function TreatmentRealizationsScreen() {
  const modalizeRef = useRef<Modalize>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [displayDate, setDisplayDate] = useState("");
  const [showAndroidPicker, setShowAndroidPicker] = useState(false);

  const openDatePicker = () => {
    if (Platform.OS === "android") {
      setShowAndroidPicker(true);
    } else {
      modalizeRef.current?.open();
    }
  };

  const onDateChange = (event, date) => {
    if (Platform.OS === "android") {
      setShowAndroidPicker(false);
    }

    if (event.type === "set" && date) {
      setSelectedDate(date);
      if (Platform.OS === "android") {
        setDisplayDate(date.toLocaleDateString("pt-BR"));
      }
    }
  };

  const handleConfirmDateIOS = () => {
    setDisplayDate(selectedDate.toLocaleDateString("pt-BR"));
    modalizeRef.current?.close();
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-200">
      <StandardHeader title="Realizações de Trato" />

      <KeyboardAwareScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        resetScrollOnKeyboardHide={true}
      >
        <View className="flex-1 p-6 justify-between">
          <VStack className="gap-4">
            <DatePickerInput
              value={displayDate}
              onPress={openDatePicker}
            />

            <TreatmentSelector />

            <VStack>
              <Text className="text-2xl font-medium text-primary-500">
                Piquete
              </Text>
              <Text className="width-full text-center text-4xl">Piquete 01</Text>
            </VStack>

            <VStack>
              <Text className="text-2xl font-medium text-primary-500">
                Qtd. Prevista
              </Text>
              <Text className="width-full text-center text-4xl">240kg</Text>
            </VStack>

            <RealizedQuantityDisplay quantity={240} />

            <VStack>
              <Text className="text-2xl font-medium text-primary-500">
                Total do dia
              </Text>
              <HStack className="justify-around">
                <Text className="width-full text-center text-4xl text-primary-500">240kg</Text>
                <Text className="width-full text-center text-4xl text-primary-500">25%</Text>
              </HStack>
            </VStack>
          </VStack>

          <ActionButtons
            onPressCancel={() => console.log("Cancel")}
            onPressConfirm={() => console.log("Next")}
          />
        </View>
      </KeyboardAwareScrollView>

      {showAndroidPicker && (
        <DateTimePicker
          mode="date"
          display="default"
          value={selectedDate}
          onChange={onDateChange}
          locale="pt-BR"
        />
      )}

      <DatePickerModal
        modalizeRef={modalizeRef}
        selectedDate={selectedDate}
        onDateChange={onDateChange}
        onConfirm={handleConfirmDateIOS}
      />
    </SafeAreaView>
  );
}