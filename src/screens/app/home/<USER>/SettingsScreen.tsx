import { SafeAreaView, View, Text, PermissionsAndroid, Platform } from "react-native";
import { useEffect, useMemo, useRef, useState } from "react";
import { Modalize } from "react-native-modalize";
import { ActionButtons } from "@/src/components/global/ActionButtons";
import { ScrollView } from "react-native-gesture-handler";
import { StandardHeader } from "@/src/components/header/StandardHeader";
import {
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectItem,
} from '@/components/ui/select';
import { ChevronDownIcon } from "lucide-react-native";
import { FormControl, FormControlLabel, FormControlLabelText } from "@/components/ui/form-control";

import { BleManager, Device } from 'react-native-ble-plx';
import { FormSelect } from "@/src/components/global/FormSelect";

const SCALE_PREFIX_MAP = {
  km3nv4: "KM3-N-",
  km3plusv3: "KM3PLUS-",
};

const SCAN_TIMEOUT = 5000;

export function SettingsScreen() {
  const modalizeRef = useRef<Modalize>(null);

  const bleManager = useMemo(() => new BleManager(), []);

  const [selectedScale, setSelectedScale] = useState<string | null>(null);
  const [connectedDevice, setConnectedDevice] = useState<Device | null>(null);
  const [connectionStatus, setConnectionStatus] = useState("Desconectado");
  const [isConnecting, setIsConnecting] = useState(false);

  useEffect(() => {
    return () => {
      bleManager.destroy();
    };
  }, [bleManager]);


  const requestBluetoothPermission = async () => {
    if (Platform.OS === 'android') {
      const apiLevel = Platform.Version;
      if (apiLevel < 31) {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Permissão de Localização',
            message: 'O App precisa de acesso à sua localização para encontrar dispositivos Bluetooth.',
            buttonNeutral: 'Pergunte-me depois',
            buttonNegative: 'Cancelar',
            buttonPositive: 'OK',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        const result = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
          PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
        ]);

        return (
          result['android.permission.BLUETOOTH_CONNECT'] === PermissionsAndroid.RESULTS.GRANTED &&
          result['android.permission.BLUETOOTH_SCAN'] === PermissionsAndroid.RESULTS.GRANTED
        );
      }
    }
    return true;
  };


  const handleConnect = async () => {
    if (!selectedScale) {
      alert("Por favor, selecione uma balança.");
      return;
    }

    const hasPermission = await requestBluetoothPermission();
    if (!hasPermission) {
      alert("Permissão de Bluetooth negada.");
      return;
    }

    setIsConnecting(true);
    setConnectionStatus("Procurando balança...");

    const searchPrefix = SCALE_PREFIX_MAP[selectedScale];

    let deviceFound = false;

    const scanTimeout = setTimeout(() => {
      if (!deviceFound) {
        bleManager.stopDeviceScan();
        setConnectionStatus("Dispositivo não encontrado.");
        setIsConnecting(false);
      }
    }, SCAN_TIMEOUT);

    bleManager.startDeviceScan(null, null, (error, device) => {
      if (error) {
        console.error(error);
        setConnectionStatus("Erro ao procurar");
        bleManager.stopDeviceScan();
        clearTimeout(scanTimeout);
        setIsConnecting(false);
        return;
      }

      if (device?.name?.toUpperCase().startsWith(searchPrefix.toUpperCase())) {
        deviceFound = true;
        clearTimeout(scanTimeout);
        bleManager.stopDeviceScan();

        console.log(`Balança encontrada: ${device.name}`);
        setConnectionStatus("Balança encontrada! Conectando...");

        device.connect()
          .then((connectedDevice) => {
            setConnectedDevice(connectedDevice);
            setConnectionStatus("Conectado!");
            console.log(`Conectado a ${connectedDevice.name}`);
          })
          .catch((connectError) => {
            console.error("Erro na conexão:", connectError);
            setConnectionStatus("Falha ao conectar");
          })
          .finally(() => {
            setIsConnecting(false);
          });
      }
    });
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-200">
      <StandardHeader title="Configurações" />

      <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
        <View className="flex-1 p-6 justify-between">
          <View>
            <FormSelect
              label="Balança"
              items={[
                { label: "KM3-N-V4", value: "km3nv4" },
                { label: "KM3PLUS-V3", value: "km3plusv3" },
              ]}
              onValueChange={setSelectedScale}
              placeholder="Selecione a balança"
            />

            <Text className="text-center mt-4 text-gray-600">
              Status: {connectionStatus}
            </Text>
          </View>

          <ActionButtons
            onPressCancel={() => console.log("Cancel")}
            onPressConfirm={handleConnect}
            cancelLabel="VOLTAR"
            confirmLabel="CONECTAR"
            confirmButtonProps={{ isDisabled: isConnecting }}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}