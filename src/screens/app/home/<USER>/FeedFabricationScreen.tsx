import { Platform, SafeAreaView, View } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { useRef, useState } from "react";
import { Input, InputField } from "@/components/ui/input";
import { DatePickerModal } from "@/src/components/global/DatePickerModal";
import { Modalize } from "react-native-modalize";
import { ActionButtons } from "@/src/components/global/ActionButtons";
import { StandardHeader } from "@/src/components/header/StandardHeader";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import { Divider } from "@/components/ui/divider";
import { FormInput } from "@/src/components/global/FormInput";
import { DatePickerInput } from "@/src/components/global/DatePickerInput";
import DateTimePicker from "@react-native-community/datetimepicker";
import { KeyboardAwareScrollView } from "@/src/components/global/KeyboardAwareScrollView";
import { FormSelect } from "@/src/components/global/FormSelect";
import { SimpleEmployeeSelect } from "@/src/components/global/SimpleEmployeeSelect";
import { useEmployees } from "@/src/hooks/useEmployees";

export function FeedFabricationScreen() {
  const modalizeRef = useRef<Modalize>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [displayDate, setDisplayDate] = useState("");
  const [showAndroidPicker, setShowAndroidPicker] = useState(false);
  const [selectedScale, setSelectedScale] = useState<string | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<string | null>(null);

  // Usar o hook para carregar funcionários do TinyBase
  const { employeeItems, loading: employeesLoading } = useEmployees();

  const openDatePicker = () => {
    if (Platform.OS === "android") {
      setShowAndroidPicker(true);
    } else {
      modalizeRef.current?.open();
    }
  };

  const onDateChange = (event, date) => {
    if (Platform.OS === "android") {
      setShowAndroidPicker(false);
    }

    if (event.type === "set" && date) {
      setSelectedDate(date);
      if (Platform.OS === "android") {
        setDisplayDate(date.toLocaleDateString("pt-BR"));
      }
    }
  };

  const handleConfirmDateIOS = () => {
    setDisplayDate(selectedDate.toLocaleDateString("pt-BR"));
    modalizeRef.current?.close();
  };

  return (
    <SafeAreaView className="flex-1 bg-secondary-200">
      <StandardHeader title={`Fabricação de\n Mistura/Ração`} />

      <KeyboardAwareScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        resetScrollOnKeyboardHide={true}
      >
        <View className="flex-1 p-6 justify-between">
          <VStack className="gap-4">
            <DatePickerInput value={displayDate} onPress={openDatePicker} />

            <FormInput label="Mistura/Ração" />
            <SimpleEmployeeSelect
              label="Funcionário"
              employees={employeeItems}
              onValueChange={setSelectedEmployee}
              placeholder="Selecione o funcionário"
              loading={employeesLoading}
            />
            <FormInput label="Quantidade" suffix="Kg" keyboardType="numeric" placeholder="0" />

            <HStack className="justify-between">
              <VStack>
                <Text className="text-2xl font-medium text-primary-500">
                  Qtd. Prevista
                </Text>
                <Text className="width-full text-start text-3xl p-4">
                  240kg
                </Text>
              </VStack>
              <View className="w-[60%]">
                <FormInput keyboardType="numeric" placeholder="0" suffix="Kg" label="Poupa Cítrica" />
              </View>
            </HStack>

            <Divider />

            <HStack className="justify-between">
              <VStack>
                <Text className="text-2xl font-medium text-primary-500">
                  Qtd. Prevista
                </Text>
                <Text className="width-full text-start text-3xl p-4">
                  240kg
                </Text>
              </VStack>
              <View className="w-[60%]">
                <FormInput keyboardType="numeric" placeholder="0" suffix="Kg" label="Poupa Cítrica" />
              </View>
            </HStack>
          </VStack>

          <ActionButtons
            onPressCancel={() => console.log("Cancel")}
            onPressConfirm={() => console.log("Next")}
          />
        </View>
      </KeyboardAwareScrollView>

      {showAndroidPicker && (
        <DateTimePicker
          mode="date"
          display="default"
          value={selectedDate}
          onChange={onDateChange}
          locale="pt-BR"
        />
      )}

      <DatePickerModal
        modalizeRef={modalizeRef}
        selectedDate={selectedDate}
        onDateChange={onDateChange}
        onConfirm={handleConfirmDateIOS}
      />
    </SafeAreaView>
  );
}
