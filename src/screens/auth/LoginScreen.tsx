import { TouchableOpacity } from "react-native";
import BackgroundImg from "@/src/assets/login/beef-bg-login.png";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { AuthLayout } from "./AuthLayout";
import { EyeIcon, EyeOffIcon } from "lucide-react-native";
import { API_CONFIG } from "@/src/constants/app-config";
import { useAuth } from "@/src/contexts/AuthContext";

export function LoginScreen({ navigation }) {
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const { signIn, loading, error } = useAuth();
  const handleState = () => setShowPassword((prev) => !prev);

  const handleLogin = async () => {
    const success = await signIn(email.trim(), password);
    if (success) navigation.navigate("Home");
  };

  return (
    <AuthLayout backgroundImage={BackgroundImg}>
      <FormControl className="w-full gap-2">
        <FormControlLabel>
          <FormControlLabelText className="text-2xl font-semibold text-white">
            E-mail
          </FormControlLabelText>
        </FormControlLabel>
        <Input className="w-full bg-white rounded-xl h-16">
          <InputField
            className="text-2xl px-4"
            autoCapitalize="none"
            keyboardType="email-address"
            value={email}
            onChangeText={setEmail}
          />
        </Input>

        <FormControlLabel className="mt-4">
          <FormControlLabelText className="text-2xl font-semibold text-white">
            Senha
          </FormControlLabelText>
        </FormControlLabel>
        <Input className="w-full bg-white rounded-xl h-16">
          <InputField
            type={showPassword ? "text" : "password"}
            className="text-2xl px-4"
            value={password}
            onChangeText={setPassword}
          />
          <InputSlot className="p-3" onPress={handleState}>
            <InputIcon
              className="text-primary-500"
              as={showPassword ? EyeIcon : EyeOffIcon}
            />
          </InputSlot>
        </Input>
      </FormControl>

      <Button
        onPress={handleLogin}
        isDisabled={loading || !email || !password}
        className=" w-full bg-primary-500 shadow-sm rounded-xl h-16 mt-6 mb-2"
      >
        <ButtonText className="text-white text-3xl">
          {loading ? "Entrando..." : "Entrar"}
        </ButtonText>
      </Button>

      {!!error && (
        <Text className="text-red-400 text-base font-semibold mb-2 self-center">
          {error}
        </Text>
      )}

      <TouchableOpacity onPress={() => navigation.navigate("ForgotPassword")}>
        <Text className="text-white text-xl font-bold self-center underline">
          Esqueceu sua senha?
        </Text>
      </TouchableOpacity>
    </AuthLayout>
  );
}
