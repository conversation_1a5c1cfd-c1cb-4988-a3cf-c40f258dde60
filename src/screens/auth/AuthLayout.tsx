import { View, StyleSheet, ImageBackground } from "react-native";
import Logo from "@/src/assets/login/logo.svg";
import { LinearGradient } from "expo-linear-gradient";
import { KeyboardAwareScrollView } from "@/src/components/global/KeyboardAwareScrollView";

export function AuthLayout({ children, backgroundImage }) {
  return (
    <ImageBackground source={backgroundImage} style={styles.container}>
      <LinearGradient
        colors={["rgba(32, 48, 120, 0.6)", "rgba(32, 48, 120, 1)"]}
        locations={[0, 0.7, 1]}
        style={StyleSheet.absoluteFill}
      />

      <KeyboardAwareScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        resetScrollOnKeyboardHide={true}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logoContainer}>
          <Logo width={200} height={300} />
        </View>

        <View style={styles.contentContainer}>{children}</View>
      </KeyboardAwareScrollView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    minHeight: '100%',
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 100,
    marginBottom: 60,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 40,
    paddingBottom: 40,
  },
});
