import BackgroundImg from "@/src/assets/login/beef-bg-login.png";
import { Input, InputField } from "@/components/ui/input";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
} from "@/components/ui/form-control";
import { Button, ButtonText } from "@/components/ui/button";
import { VStack } from "@/components/ui/vstack";
import { AuthLayout } from "./AuthLayout";

export function ForgotPasswordScreen({ navigation }) {
  return (
    <AuthLayout backgroundImage={BackgroundImg}>
      <FormControl className="w-full gap-2">
        <FormControlLabel>
          <FormControlLabelText className="text-2xl font-semibold text-white">
            E-mail
          </FormControlLabelText>
        </FormControlLabel>
        <Input className="w-full bg-white rounded-xl h-16">
          <InputField className="text-2xl px-4" />
        </Input>
      </FormControl>

      <VStack className="w-full mt-6 gap-6">
        <Button className="w-full bg-primary-500 shadow-sm rounded-xl h-16">
          <ButtonText className="text-white text-3xl">Enviar</ButtonText>
        </Button>

        <Button
          onPress={() => navigation.navigate("Login")}
          className="w-full bg-white shadow-sm rounded-xl h-16"
        >
          <ButtonText className="text-primary-500 text-3xl">Voltar</ButtonText>
        </Button>
      </VStack>
    </AuthLayout>
  );
}
