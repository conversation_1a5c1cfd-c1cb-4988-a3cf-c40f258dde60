{"name": "beefsystem", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start --dev-client", "android": "DARK_MODE=media expo run:android", "ios": "DARK_MODE=media expo run:ios", "web": "DARK_MODE=media expo start --web"}, "dependencies": {"@expo/html-elements": "^0.12.5", "@gluestack-ui/core": "3.0.0", "@gluestack-ui/utils": "3.0.0", "@gluestack/ui-next-adapter": "3.0.0", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "11.4.1", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "axios": "^1.11.0", "babel-plugin-module-resolver": "^5.0.2", "buffer": "^6.0.3", "expo": "~53.0.20", "expo-background-task": "~0.2.8", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-task-manager": "~13.1.6", "lucide-react-native": "^0.543.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "^19.1.1", "react-native": "0.79.5", "react-native-ble-plx": "^3.5.0", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.24.0", "react-native-modalize": "^2.1.1", "react-native-portalize": "^1.0.7", "react-native-reanimated": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.12.0", "react-native-svg-transformer": "^1.5.1", "react-native-worklets": "^0.5.0", "tailwindcss": "^3.4.17", "tinybase": "^6.6.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "jscodeshift": "^0.15.2", "typescript": "~5.8.3"}, "resolutions": {"react-native-svg": "15.12.0"}, "private": true}